# Robust tracker configuration for weed burning
# Uses the most reliable tracker available

tracker_type: "auto"  # Automatically select best available tracker

# Detection parameters
conf_threshold: 0.25
iou_threshold: 0.45

# Tracking parameters
max_age: 30          # Maximum frames to keep lost tracks
min_hits: 3          # Minimum detections before confirming track
iou_threshold: 0.3   # IoU threshold for track association

# Fallback options
fallback_to_detection: true  # Use detection-only if tracking fails
