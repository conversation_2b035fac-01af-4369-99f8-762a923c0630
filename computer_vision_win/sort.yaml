# SORT tracker configuration
# Simple Online and Realtime Tracking (SORT) algorithm

tracker_type: SORT

# SORT specific parameters
max_disappeared: 30        # Maximum number of frames to keep lost tracks
max_distance: 50          # Maximum distance for track association
min_hits: 3              # Minimum hits before a track is confirmed
iou_threshold: 0.3       # IoU threshold for track association

# Detection parameters
conf_thres: 0.25         # Confidence threshold for detections
iou_thres: 0.45          # IoU threshold for NMS

# Kalman filter parameters
kalman:
  process_noise: 1e-2    # Process noise covariance
  measurement_noise: 1e-1 # Measurement noise covariance
  error_covariance: 1e-1  # Initial error covariance

# Track management
track_buffer: 30         # Number of frames to buffer lost tracks
frame_rate: 30           # Expected frame rate
