#!/usr/bin/env python3
"""
Fix tracker issues and test available trackers
"""

from ultralytics import YOLO
import cv2
import numpy as np
import os

def create_test_frame():
    """Create a test frame with some objects"""
    frame = np.zeros((480, 640, 3), dtype=np.uint8)
    # Add some white rectangles as test objects
    cv2.rectangle(frame, (100, 100), (200, 200), (255, 255, 255), -1)
    cv2.rectangle(frame, (300, 200), (400, 300), (255, 255, 255), -1)
    cv2.rectangle(frame, (450, 50), (550, 150), (255, 255, 255), -1)
    return frame

def test_available_trackers():
    """Test which trackers are available and working"""
    
    print("🔧 FIXING TRACKER ISSUES")
    print("=" * 50)
    
    # Load model
    try:
        model = YOLO("best.pt")
        print("✅ Loaded custom model: best.pt")
    except:
        try:
            model = YOLO("yolov8n.pt")
            print("✅ Loaded default model: yolov8n.pt")
        except Exception as e:
            print(f"❌ Could not load any model: {e}")
            return []

    # Create test frame
    test_frame = create_test_frame()
    
    # List of trackers to test
    trackers_to_test = [
        "botsort.yaml",
        "bytetrack.yaml",
    ]
    
    working_trackers = []
    
    print(f"\n🧪 Testing {len(trackers_to_test)} trackers...")
    
    for tracker in trackers_to_test:
        print(f"\n🔍 Testing {tracker}...")
        try:
            # Test the tracker
            results = model.track(test_frame, conf=0.1, persist=True, tracker=tracker)
            
            # Check if we got results
            detection_count = 0
            tracking_ids = []
            
            for result in results:
                if result.boxes is not None:
                    detection_count += len(result.boxes)
                    for box in result.boxes:
                        if hasattr(box, 'id') and box.id is not None:
                            tracking_ids.append(int(box.id[0]))
            
            if detection_count > 0:
                print(f"✅ {tracker} - SUCCESS")
                print(f"   Detections: {detection_count}")
                if tracking_ids:
                    print(f"   Tracking IDs: {tracking_ids}")
                else:
                    print(f"   No tracking IDs (detection only)")
                working_trackers.append(tracker)
            else:
                print(f"⚠️ {tracker} - No detections found")
                
        except Exception as e:
            print(f"❌ {tracker} - FAILED: {e}")
    
    # Test detection without tracking
    print(f"\n🔍 Testing detection without tracking...")
    try:
        results = model(test_frame, conf=0.1, stream=True)
        detection_count = 0
        for result in results:
            if result.boxes is not None:
                detection_count += len(result.boxes)
        
        if detection_count > 0:
            print(f"✅ Detection without tracking - SUCCESS ({detection_count} detections)")
        else:
            print(f"⚠️ Detection without tracking - No detections")
            
    except Exception as e:
        print(f"❌ Detection without tracking - FAILED: {e}")
    
    return working_trackers

def create_robust_tracker_config():
    """Create a robust tracker configuration that works"""
    
    print(f"\n📝 Creating robust tracker configuration...")
    
    # Create a simple tracker config that should work
    robust_config = """# Robust tracker configuration for weed burning
# Uses the most reliable tracker available

tracker_type: "auto"  # Automatically select best available tracker

# Detection parameters
conf_threshold: 0.25
iou_threshold: 0.45

# Tracking parameters
max_age: 30          # Maximum frames to keep lost tracks
min_hits: 3          # Minimum detections before confirming track
iou_threshold: 0.3   # IoU threshold for track association

# Fallback options
fallback_to_detection: true  # Use detection-only if tracking fails
"""
    
    try:
        with open("robust_tracker.yaml", "w") as f:
            f.write(robust_config)
        print("✅ Created robust_tracker.yaml")
    except Exception as e:
        print(f"❌ Failed to create robust_tracker.yaml: {e}")

def main():
    """Main function to fix tracker issues"""
    
    # Test available trackers
    working_trackers = test_available_trackers()
    
    # Create robust config
    create_robust_tracker_config()
    
    # Print summary
    print(f"\n📊 SUMMARY")
    print("=" * 30)
    
    if working_trackers:
        print(f"✅ Working trackers: {working_trackers}")
        print(f"🎯 Recommended tracker: {working_trackers[0]}")
        
        # Update recommendation
        print(f"\n💡 RECOMMENDATION:")
        print(f"Use this tracker in your code:")
        print(f'   results = model.track(frame, conf=0.3, persist=True, tracker="{working_trackers[0]}")')
        
    else:
        print(f"❌ No trackers working")
        print(f"💡 RECOMMENDATION:")
        print(f"Use detection only:")
        print(f'   results = model(frame, conf=0.3, stream=True)')
    
    print(f"\n🔧 NEXT STEPS:")
    print(f"1. Run your weed burning script")
    print(f"2. It will automatically use the best available tracker")
    print(f"3. If tracking fails, it will fallback to detection-only")

if __name__ == "__main__":
    main()
