from ultralytics import YOLO
import cv2
import freenect
import numpy as np
import time
import pyfirmata
import json
import glob
import os
from scipy.interpolate import griddata

# Load YOLO model
model = YOLO("best.pt")

# Arduino setup
board = pyfirmata.Arduino('/dev/ttyACM0')  # Change path as needed
it = pyfirmata.util.Iterator(board)
it.start()

# Define servo and laser pins
servo_x = board.get_pin('d:10:s')     # D10: Servo X
servo_y = board.get_pin('d:9:s')      # D9: Servo Y
laser = board.get_pin('d:8:o')        # D8: Laser ON/OFF

# Camera dimensions
CAMERA_WIDTH = 640
CAMERA_HEIGHT = 480

# Servo ranges (will be loaded from calibration)
SERVO_X_MIN = 70
SERVO_X_MAX = 120
SERVO_Y_MIN = 70
SERVO_Y_MAX = 122

# Continuous detection - track burned positions instead of IDs
burned_positions = []  # List of burned positions
BURN_RADIUS = 40  # Don't burn within this radius of a previously burned position

# Calibration data
calibration_data = None
interpolator_x = None
interpolator_y = None

def get_kinect_frame():
    """Capture frame from Kinect v1"""
    _, _ = freenect.sync_get_depth()
    video, _ = freenect.sync_get_video()
    return cv2.cvtColor(video, cv2.COLOR_RGB2BGR)

def load_calibration():
    """Load the most recent calibration file"""
    global calibration_data, interpolator_x, interpolator_y
    global SERVO_X_MIN, SERVO_X_MAX, SERVO_Y_MIN, SERVO_Y_MAX

    files = glob.glob("servo_calibration_*.json")
    if not files:
        print("❌ No calibration files found! Creating fallback...")
        basic_calibration = {
            "timestamp": "fallback",
            "servo_ranges": {"x_min": SERVO_X_MIN, "x_max": SERVO_X_MAX, "y_min": SERVO_Y_MIN, "y_max": SERVO_Y_MAX},
            "camera_dimensions": {"width": CAMERA_WIDTH, "height": CAMERA_HEIGHT},
            "calibration_points": [
                {"camera": [136, 234], "servo": [76, 92]},
                {"camera": [320, 240], "servo": [88, 85]},
                {"camera": [500, 150], "servo": [100, 78]}
            ]
        }
        with open("servo_calibration_fallback.json", 'w') as f:
            json.dump(basic_calibration, f, indent=2)
        files = ["servo_calibration_fallback.json"]

    latest_file = max(files, key=os.path.getctime)
    try:
        with open(latest_file, 'r') as f:
            calibration_data = json.load(f)

        print(f"📂 Loaded calibration from: {latest_file}")

        # Update servo ranges
        ranges = calibration_data['servo_ranges']
        SERVO_X_MIN = ranges['x_min']
        SERVO_X_MAX = ranges['x_max']
        SERVO_Y_MIN = ranges['y_min']
        SERVO_Y_MAX = ranges['y_max']

        # Create interpolators
        points = calibration_data['calibration_points']
        camera_coords = np.array([point['camera'] for point in points])
        servo_x_coords = np.array([point['servo'][0] for point in points])
        servo_y_coords = np.array([point['servo'][1] for point in points])

        def safe_interpolator_x(x, y):
            try:
                if len(points) >= 3:
                    result = griddata(camera_coords, servo_x_coords, (x, y), method='linear', fill_value=np.nan)
                    if np.isnan(result):
                        result = griddata(camera_coords, servo_x_coords, (x, y), method='nearest')
                    return result
                else:
                    return griddata(camera_coords, servo_x_coords, (x, y), method='nearest')
            except:
                return griddata(camera_coords, servo_x_coords, (x, y), method='nearest')

        def safe_interpolator_y(x, y):
            try:
                if len(points) >= 3:
                    result = griddata(camera_coords, servo_y_coords, (x, y), method='linear', fill_value=np.nan)
                    if np.isnan(result):
                        result = griddata(camera_coords, servo_y_coords, (x, y), method='nearest')
                    return result
                else:
                    return griddata(camera_coords, servo_y_coords, (x, y), method='nearest')
            except:
                return griddata(camera_coords, servo_y_coords, (x, y), method='nearest')

        interpolator_x = safe_interpolator_x
        interpolator_y = safe_interpolator_y
        return True

    except Exception as e:
        print(f"❌ Error loading calibration: {e}")
        return False

def camera_to_servo_angles(camera_x, camera_y):
    """Convert camera coordinates to servo angles"""
    if interpolator_x is None or interpolator_y is None:
        return 88, 85

    try:
        servo_x = interpolator_x(camera_x, camera_y)
        servo_y = interpolator_y(camera_x, camera_y)

        if servo_x is None or servo_y is None or np.isnan(servo_x) or np.isnan(servo_y):
            return 88, 85

        servo_x = max(SERVO_X_MIN, min(SERVO_X_MAX, float(servo_x)))
        servo_y = max(SERVO_Y_MIN, min(SERVO_Y_MAX, float(servo_y)))
        return int(servo_x), int(servo_y)
    except:
        return 88, 85

def compute_center(box):
    """Compute center of bounding box"""
    x1, y1, x2, y2 = box
    return [int((x1 + x2) / 2), int((y1 + y2) / 2)]

def is_near_burned_position(center, burned_positions, radius=40):
    """Check if position is near a previously burned position"""
    for burned_pos in burned_positions:
        distance = np.linalg.norm(np.array(center) - np.array(burned_pos))
        if distance < radius:
            return True, distance
    return False, 0

def main():
    """Main continuous weed burning loop"""
    print("🔥 CONTINUOUS WEED BURNING SYSTEM")
    print("=" * 50)

    if not load_calibration():
        print("❌ Cannot start without calibration data!")
        return

    # Initialize servo to center
    servo_x.write(88)
    servo_y.write(85)
    time.sleep(1)

    print("\n🎯 Starting continuous weed burning...")
    print("🔄 Will detect and burn ALL new weeds continuously")
    print("Press 'q' to quit")

    frame_count = 0

    try:
        while True:
            frame = get_kinect_frame()
            frame = cv2.resize(frame, (CAMERA_WIDTH, CAMERA_HEIGHT))
            frame_count += 1

            print(f"\n--- Frame {frame_count} ---")

            # Use fresh detection every few frames to ensure new weeds are found
            if frame_count % 5 == 1:
                # Fresh detection without tracking
                results = model(frame, conf=0.25, stream=True)
                detection_method = "Fresh Detection"
            else:
                # Use tracking for consistency - try multiple trackers
                tracking_success = False
                for tracker_name in ["botsort.yaml", "bytetrack.yaml"]:
                    try:
                        results = model.track(frame, conf=0.25, persist=True, tracker=tracker_name)
                        detection_method = f"{tracker_name.split('.')[0].upper()} Tracking"
                        tracking_success = True
                        break
                    except Exception as e:
                        print(f"Tracker {tracker_name} failed: {e}")
                        continue

                if not tracking_success:
                    # Fallback to detection only
                    results = model(frame, conf=0.25, stream=True)
                    detection_method = "Fallback Detection"

            print(f"Detection method: {detection_method}")

            # Process all detections
            total_detections = 0
            new_weeds_found = 0
            burned_this_frame = 0

            for result in results:
                boxes = result.boxes
                if boxes is None:
                    continue

                total_detections += len(boxes)
                print(f"Found {len(boxes)} detections")

                for i, box in enumerate(boxes):
                    x1, y1, x2, y2 = map(int, box.xyxy[0])
                    confidence = float(box.conf[0])
                    center = compute_center((x1, y1, x2, y2))

                    print(f"  Detection {i}: Center=({center[0]}, {center[1]}), Conf={confidence:.2f}")

                    # Check if this position was already burned
                    is_burned, distance = is_near_burned_position(center, burned_positions, BURN_RADIUS)
                    
                    if is_burned:
                        color = (0, 0, 255)  # Red for burned area
                        label = f"BURNED (d:{distance:.0f})"
                        print(f"    Skipping - too close to burned position (distance: {distance:.1f})")
                    else:
                        new_weeds_found += 1
                        # BURN IMMEDIATELY if confidence is decent
                        if confidence > 0.2:  # Low threshold for immediate action
                            print(f"🔥 BURNING NEW WEED - Conf: {confidence:.2f}")
                            
                            # Get servo angles
                            servo_angle_x, servo_angle_y = camera_to_servo_angles(center[0], center[1])
                            
                            # Move and burn
                            servo_x.write(servo_angle_x)
                            servo_y.write(servo_angle_y)
                            time.sleep(0.3)  # Quick servo movement
                            
                            laser.write(1)
                            print(f"🎯 LASER ON at ({servo_angle_x}°, {servo_angle_y}°)")
                            time.sleep(1.2)  # Burn time
                            laser.write(0)
                            print(f"🔥 LASER OFF - Position burned!")
                            
                            # Remember this position
                            burned_positions.append(center)
                            burned_this_frame += 1
                            
                            color = (0, 255, 0)  # Green for just burned
                            label = f"JUST BURNED"
                        else:
                            color = (255, 255, 0)  # Yellow for low confidence
                            label = f"LOW CONF: {confidence:.2f}"
                            print(f"    Confidence too low: {confidence:.2f}")

                    # Draw detection
                    cv2.rectangle(frame, (x1, y1), (x2, y2), color, 2)
                    cv2.putText(frame, label, (x1, y1 - 10),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
                    cv2.circle(frame, tuple(center), 5, color, -1)
                    cv2.putText(frame, f"({center[0]},{center[1]})",
                               (center[0] + 10, center[1]),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

            # Draw burned positions as circles
            for burned_pos in burned_positions:
                cv2.circle(frame, tuple(burned_pos), BURN_RADIUS, (0, 0, 255), 1)
                cv2.circle(frame, tuple(burned_pos), 3, (0, 0, 255), -1)

            # Print frame summary
            print(f"Frame Summary: {total_detections} total, {new_weeds_found} new, {burned_this_frame} burned")
            print(f"Total burned positions: {len(burned_positions)}")

            # Draw stats
            cv2.putText(frame, f"Frame: {frame_count} | Method: {detection_method}",
                       (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            cv2.putText(frame, f"Total: {total_detections} | New: {new_weeds_found} | Burned: {len(burned_positions)}",
                       (10, 55), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)

            cv2.imshow("Continuous Weed Burner", frame)

            if cv2.waitKey(1) & 0xFF == ord('q'):
                break

    except KeyboardInterrupt:
        print("\n🛑 Stopping...")

    finally:
        laser.write(0)
        servo_x.write(88)
        servo_y.write(85)
        board.exit()
        cv2.destroyAllWindows()
        print("✅ System shutdown complete")

if __name__ == "__main__":
    main()
